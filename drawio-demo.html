<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Draw.io Editor Integration Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        #editor-container {
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        #drawio-iframe {
            width: 100%;
            height: 700px;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }
        .status.loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .sample-diagrams {
            margin: 20px 0;
        }
        .sample-btn {
            background: #28a745;
            margin: 2px;
            padding: 8px 12px;
            font-size: 12px;
        }
        .sample-btn:hover {
            background: #1e7e34;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-size: 14px;
        }
        .editor-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Draw.io Editor Integration Demo</h1>
        
        <div class="instructions">
            <strong>How to use:</strong>
            <ul>
                <li>Click "Open Editor" to load the draw.io interface</li>
                <li>Use the toolbar on the left to add shapes</li>
                <li>Drag and drop elements to move them</li>
                <li>Connect shapes using the connection tool</li>
                <li>Edit text by double-clicking on elements</li>
                <li>Click "Get Diagram XML" to see the generated code</li>
                <li>Try the sample diagrams to see different templates</li>
            </ul>
        </div>

        <div class="controls">
            <button id="open-editor" onclick="openEditor()">Open Draw.io Editor</button>
            <button id="load-sample" onclick="loadSampleDiagram()" disabled>Load Sample Flowchart</button>
            <button id="get-xml" onclick="getDiagramXML()" disabled>Get Diagram XML</button>
            <button id="clear-editor" onclick="clearEditor()" disabled>Clear Editor</button>
        </div>

        <div class="sample-diagrams">
            <strong>Quick Samples:</strong><br>
            <button class="sample-btn" onclick="loadSample('basic')" disabled>Basic Flowchart</button>
            <button class="sample-btn" onclick="loadSample('org')" disabled>Org Chart</button>
            <button class="sample-btn" onclick="loadSample('network')" disabled>Network Diagram</button>
            <button class="sample-btn" onclick="loadSample('empty')" disabled>Blank Canvas</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div id="editor-container">
            <div id="editor-placeholder" class="editor-placeholder">
                <h3>Draw.io Editor</h3>
                <p>Click "Open Editor" to load the interactive diagram editor</p>
                <p><small>The editor will appear here with full toolbar and editing capabilities</small></p>
            </div>
            <iframe 
                id="drawio-iframe"
                style="display: none;"
                allow="clipboard-read; clipboard-write"
                allowfullscreen
            ></iframe>
        </div>

        <div id="output-container" style="display: none; margin-top: 20px;">
            <h3>Generated XML:</h3>
            <textarea id="xml-output" style="width: 100%; height: 200px; font-family: monospace; padding: 10px;" readonly></textarea>
        </div>
    </div>

    <script>
        let drawIoIframe = null;
        let isEditorLoaded = false;

        // Sample diagrams in draw.io XML format
        const sampleDiagrams = {
            basic: `
<mxfile>
  <diagram name="Page-1" id="ePSVOPC1a0s8TtCA0e2a">
    <mxGraphModel dx="1426" dy="881" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="Start" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="100" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="Process" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="180" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="End" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="260" width="80" height="40" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="140" as="sourcePoint"/>
            <mxPoint x="140" y="180" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
        <mxCell id="6" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="140" y="220" as="sourcePoint"/>
            <mxPoint x="140" y="260" as="targetPoint"/>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>`,
            org: `
<mxfile>
  <diagram name="Page-1" id="ePSVOPC1a0s8TtCA0e2a">
    <mxGraphModel dx="1426" dy="881" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="CEO" style="swimlane;startSize=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="100" width="120" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="Manager A" style="swimlane;startSize=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="200" width="120" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="Manager B" style="swimlane;startSize=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="300" y="200" width="120" height="60" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1" source="2" target="3"/>
        <mxCell id="6" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" edge="1" source="2" target="4"/>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>`,
            empty: `<mxfile><diagram name="Page-1"><mxGraphModel dx="1426" dy="881" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0"><root><mxCell id="0"/><mxCell id="1" parent="0"/></root></mxGraphModel></diagram></mxfile>`
        };

        function openEditor() {
            showStatus('Loading draw.io editor...', 'loading');
            
            const iframe = document.getElementById('drawio-iframe');
            const placeholder = document.getElementById('editor-placeholder');
            const editorContainer = document.getElementById('editor-container');
            
            // Hide placeholder and show iframe
            placeholder.style.display = 'none';
            iframe.style.display = 'block';
            
            // Use the official draw.io embed URL with proper parameters
            const drawIoUrl = new URL('https://embed.diagrams.net/');
            drawIoUrl.searchParams.set('embed', '1');
            drawIoUrl.searchParams.set('ui', 'atlas');
            drawIoUrl.searchParams.set('spin', '1');
            drawIoUrl.searchParams.set('proto', 'json');
            drawIoUrl.searchParams.set('configure', '1');
            drawIoUrl.searchParams.set('libraries', '1');
            drawIoUrl.searchParams.set('noExitBtn', '1');
            drawIoUrl.searchParams.set('noSaveBtn', '1');
            drawIoUrl.searchParams.set('saveAndExit', '0');
            
            iframe.src = drawIoUrl.toString();
            drawIoIframe = iframe;
            
            // Set up message listener for draw.io
            window.addEventListener('message', handleDrawIoMessage);
            
            // Enable buttons after a short delay (editor takes time to load)
            setTimeout(() => {
                isEditorLoaded = true;
                enableButtons();
                showStatus('Editor ready! Drag shapes from the left toolbar to create diagrams.', 'success');
            }, 2000);
        }

        function handleDrawIoMessage(event) {
            // Handle messages from draw.io
            console.log('Message from draw.io:', event.data);
            
            if (event.data && event.data.event === 'init') {
                console.log('Draw.io editor initialized successfully');
                isEditorLoaded = true;
                enableButtons();
                showStatus('Editor ready! Drag shapes from the left toolbar to create diagrams.', 'success');
            }
        }

        function loadSampleDiagram() {
            if (!isEditorLoaded) {
                showStatus('Editor not ready yet. Please wait...', 'loading');
                return;
            }
            loadDiagramXML(sampleDiagrams.basic);
        }

        function loadSample(type) {
            if (!isEditorLoaded) {
                showStatus('Editor not ready yet. Please wait...', 'loading');
                return;
            }
            loadDiagramXML(sampleDiagrams[type]);
        }

        function loadDiagramXML(xml) {
            if (!drawIoIframe || !isEditorLoaded) {
                showStatus('Editor not ready', 'loading');
                return;
            }
            
            // Clean up XML string
            const cleanXml = xml.replace(/\n\s+/g, '');
            
            const message = {
                action: 'load',
                xml: cleanXml
            };
            
            // Wait a bit for the editor to be fully ready
            setTimeout(() => {
                drawIoIframe.contentWindow.postMessage(JSON.stringify(message), '*');
                showStatus('Sample diagram loaded! Try editing it.', 'success');
            }, 500);
        }

        function getDiagramXML() {
            if (!drawIoIframe || !isEditorLoaded) {
                showStatus('Editor not ready', 'loading');
                return;
            }
            
            const message = {
                action: 'export',
                format: 'xml'
            };
            drawIoIframe.contentWindow.postMessage(JSON.stringify(message), '*');
            
            showStatus('Requesting diagram data...', 'loading');
        }

        function clearEditor() {
            if (!isEditorLoaded) return;
            loadDiagramXML(sampleDiagrams.empty);
            showStatus('Editor cleared!', 'success');
        }

        function enableButtons() {
            document.getElementById('load-sample').disabled = false;
            document.getElementById('get-xml').disabled = false;
            document.getElementById('clear-editor').disabled = false;
            
            // Enable sample buttons
            document.querySelectorAll('.sample-btn').forEach(btn => {
                btn.disabled = false;
            });
        }

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 5000);
            }
        }

        // Set up a global message handler for draw.io responses
        window.addEventListener('message', function(event) {
            // Handle export responses
            if (event.data && event.data.xml) {
                const outputContainer = document.getElementById('output-container');
                const xmlOutput = document.getElementById('xml-output');
                
                xmlOutput.value = event.data.xml;
                outputContainer.style.display = 'block';
                
                showStatus('Diagram XML retrieved successfully!', 'success');
            }
            
            // Handle other draw.io events
            if (event.data && event.data.event === 'export') {
                console.log('Export completed:', event.data);
            }
        });

        // Alternative: Try a simpler approach with direct embedding
        function openEditorSimple() {
            const iframe = document.getElementById('drawio-iframe');
            const placeholder = document.getElementById('editor-placeholder');
            
            placeholder.style.display = 'none';
            iframe.style.display = 'block';
            
            // Simple direct embed
            iframe.src = 'https://embed.diagrams.net/?embed=1&ui=atlas&spin=1&proto=json&libraries=1';
            
            drawIoIframe = iframe;
            
            setTimeout(() => {
                isEditorLoaded = true;
                enableButtons();
                showStatus('Editor loaded! Use the toolbar on the left to create diagrams.', 'success');
            }, 3000);
        }

        // Use the simple approach
        document.getElementById('open-editor').onclick = openEditorSimple;
    </script>
</body>
</html>