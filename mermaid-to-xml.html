<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Mermaid to Draw.io</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        textarea { width: 100%; height: 150px; font-family: monospace; padding: 10px; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #drawio-container { border: 2px solid #ddd; border-radius: 8px; overflow: hidden; margin: 20px 0; }
        #drawio-iframe { width: 100%; height: 600px; border: none; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; display: none; }
        .success { background: #d1ecf1; color: #0c5460; }
        .loading { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .controls { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Automated Mermaid to Draw.io</h1>
        
        <div class="controls">
            <h3>1. Enter Mermaid.js Code:</h3>
            <textarea id="mermaid-code">graph TD
    A[Start] --> B[Process Data]
    B --> C{Decision}
    C -->|Yes| D[Success]
    C -->|No| E[Failure]
    D --> F[End]
    E --> F</textarea>
            
            <button onclick="autoPasteToDrawIO()">Auto-Paste to Draw.io</button>
            <button onclick="loadSample()">Load Sample</button>
        </div>

        <div id="status" class="status"></div>

        <div class="controls">
            <h3>2. Draw.io Editor:</h3>
            <div id="drawio-container">
                <iframe id="drawio-iframe" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        // Sample Mermaid code
        const sampleMermaid = `graph TD
    A[Start] --> B[Process Data]
    B --> C{Decision Point}
    C -->|Yes| D[Success Path]
    C -->|No| E[Alternative Path]
    D --> F[Complete]
    E --> F`;

        function loadSample() {
            document.getElementById('mermaid-code').value = sampleMermaid;
            showStatus('Sample Mermaid code loaded', 'success');
        }

        function autoPasteToDrawIO() {
            const mermaidCode = document.getElementById('mermaid-code').value.trim();
            if (!mermaidCode) {
                showStatus('Please enter Mermaid code first', 'error');
                return;
            }

            showStatus('Opening Draw.io and setting up automation...', 'loading');
            
            // Initialize Draw.io editor
            initializeDrawIO(mermaidCode);
        }
function initializeDrawIO(mermaidCode) {
    const iframe = document.getElementById('drawio-iframe');
    
    console.log('🚀 Starting Draw.io initialization...');
    
    // Load Draw.io with specific configuration for Mermaid
    const drawIoUrl = new URL('https://embed.diagrams.net/');
    drawIoUrl.searchParams.set('embed', '1');
    drawIoUrl.searchParams.set('ui', 'min');
    drawIoUrl.searchParams.set('spin', '1');
    drawIoUrl.searchParams.set('proto', 'json');
    drawIoUrl.searchParams.set('configure', '1');
    drawIoUrl.searchParams.set('libraries', '1');
    
    console.log('🎯 Draw.io URL:', drawIoUrl.toString());
    iframe.src = drawIoUrl.toString();

    // Set up message listener FIRST - before iframe loads
    window.addEventListener('message', function(event) {
        console.log('📨 Message received from iframe:', event.data);
        console.log('📮 Message origin:', event.origin);
        
        // Only process messages from draw.io
        if (event.origin !== 'https://embed.diagrams.net') {
            console.log('🚫 Ignoring message from non-draw.io origin');
            return;
        }
        
        handleDrawIoMessage(event, mermaidCode);
    });

    iframe.onload = function() {
        console.log('✅ Iframe onload event fired - Draw.io should be loading');
        console.log('⏳ Waiting for Draw.io "init" event before starting automation...');
        
        showStatus('Draw.io loading... Waiting for initialization...', 'loading');
        
        // Don't start automation yet - wait for the 'init' event
        // The timeout below is just a fallback
        setTimeout(() => {
            console.log('🕐 Fallback timeout reached - checking if we missed init event');
            if (!window.drawIoInitialized) {
                console.log('❌ Still no init event - Draw.io might not be fully loaded');
                showStatus('Draw.io taking longer to load...', 'loading');
            }
        }, 10000);
    };

    iframe.onerror = function(error) {
        console.error('❌ Iframe loading error:', error);
    };
}

function handleDrawIoMessage(event, mermaidCode) {
    console.log('🔍 Analyzing Draw.io message...');
    
    if (event.data) {
        let messageData = event.data;
        
        // Parse if it's a string
        if (typeof event.data === 'string') {
            try {
                messageData = JSON.parse(event.data);
                console.log('✅ Successfully parsed JSON:', messageData);
            } catch (e) {
                console.log('❌ Could not parse message as JSON:', e.message);
                return;
            }
        }
        
        console.log('📋 Message event:', messageData.event);
        
        if (messageData.event === 'init') {
            console.log('🎉 DRAW.IO INITIALIZED SUCCESSFULLY!');
            console.log('✅ Draw.io is ready for automation');
            window.drawIoInitialized = true;
            showStatus('Draw.io initialized. Starting Mermaid import...', 'success');
            
            // Wait a bit more for UI to be fully ready
            setTimeout(() => {
                startAutomation(mermaidCode);
            }, 2000);
        }
        else if (messageData.event === 'configure') {
            console.log('⚙️ Draw.io configuration received');
            // This is normal - draw.io is configuring itself
        }
        else if (messageData.event === 'loading') {
            console.log('📥 Draw.io is loading resources...');
        }
        else {
            console.log('📬 Other Draw.io event:', messageData.event);
        }
    }
}

function startAutomation(mermaidCode) {
    const iframe = document.getElementById('drawio-iframe');
    
    console.log('🔧 Starting automation process...');
    
    if (!iframe.contentWindow) {
        console.log('❌ Iframe contentWindow is not available');
        showStatus('Draw.io not ready. Retrying...', 'loading');
        setTimeout(() => startAutomation(mermaidCode), 1000);
        return;
    }

    console.log('✅ Iframe contentWindow is available');
    console.log('🚀 Attempting to send message to Draw.io...');
    
    // Use a different approach - don't send injectScript as JSON string
    // Instead, send a simple message that Draw.io can understand
    const simpleMessage = {
        action: 'hello',
        timestamp: Date.now()
    };
    
    try {
        console.log('📤 Sending test message to Draw.io:', simpleMessage);
        iframe.contentWindow.postMessage(simpleMessage, '*');
        
        // Now try the automation with a simpler approach
        setTimeout(() => {
            attemptAutomation(mermaidCode);
        }, 1000);
        
    } catch (error) {
        console.error('❌ Error sending message to Draw.io:', error);
    }
}

function attemptAutomation(mermaidCode) {
    console.log('🎯 Attempting automation with simplified approach...');
    
    // Instead of complex script injection, try a direct approach
    const iframe = document.getElementById('drawio-iframe');
    
    // Send a message that might trigger Mermaid import
    const mermaidMessage = {
        action: 'import',
        format: 'mermaid',
        data: mermaidCode
    };
    
    console.log('📤 Sending Mermaid import request:', mermaidMessage);
    
    try {
        iframe.contentWindow.postMessage(mermaidMessage, '*');
        showStatus('Mermaid import requested...', 'loading');
    } catch (error) {
        console.error('❌ Error sending Mermaid import:', error);
        showStatus('Failed to send Mermaid code', 'error');
    }
}

        function clickInsertMenu(iframe, mermaidCode) {
            // Inject script to click the insert menu
            const clickScript = `
                // Find and click the insert menu
                function findAndClickInsert() {
                    // Method 1: Look for gear menu button
                    const gearMenu = document.querySelector('[title*="Insert"]') || 
                                   document.querySelector('[title*="insert"]') ||
                                   document.querySelector('[aria-label*="Insert"]') ||
                                   document.querySelector('.geButton');
                    
                    if (gearMenu) {
                        gearMenu.click();
                        return true;
                    }
                    
                    // Method 2: Look for + button
                    const plusBtn = document.querySelector('[title*="+"]') ||
                                   document.querySelector('[title*="add"]') ||
                                   document.querySelector('[aria-label*="Add"]');
                    
                    if (plusBtn) {
                        plusBtn.click();
                        return true;
                    }
                    
                    // Method 3: Look for menu items with text
                    const menus = document.querySelectorAll('button, div[role="button"], a');
                    for (let menu of menus) {
                        if (menu.textContent && menu.textContent.includes('Insert')) {
                            menu.click();
                            return true;
                        }
                    }
                    
                    return false;
                }
                
                // Try to click insert menu
                if (findAndClickInsert()) {
                    // Success - notify parent
                    window.parent.postMessage({action: 'insertClicked', success: true}, '*');
                    
                    // Wait a bit then look for Mermaid option
                    setTimeout(() => {
                        findAndClickMermaid();
                    }, 1000);
                } else {
                    window.parent.postMessage({action: 'insertClicked', success: false}, '*');
                }
                
                function findAndClickMermaid() {
                    // Look for Mermaid option in the menu
                    const menuItems = document.querySelectorAll('.geItem, [role="menuitem"], .mxPopupMenuItem');
                    
                    for (let item of menuItems) {
                        const text = item.textContent || item.innerText || '';
                        if (text.toLowerCase().includes('mermaid')) {
                            item.click();
                            window.parent.postMessage({action: 'mermaidClicked', success: true}, '*');
                            return;
                        }
                    }
                    
                    // Alternative: Look for "Advanced" or "More" then Mermaid
                    for (let item of menuItems) {
                        const text = item.textContent || item.innerText || '';
                        if (text.toLowerCase().includes('advanced') || text.toLowerCase().includes('more')) {
                            item.click();
                            setTimeout(() => {
                                findMermaidInSubmenu();
                            }, 1000);
                            return;
                        }
                    }
                    
                    window.parent.postMessage({action: 'mermaidClicked', success: false}, '*');
                }
                
                function findMermaidInSubmenu() {
                    const subItems = document.querySelectorAll('.geItem, [role="menuitem"], .mxPopupMenuItem');
                    for (let item of subItems) {
                        const text = item.textContent || item.innerText || '';
                        if (text.toLowerCase().includes('mermaid')) {
                            item.click();
                            window.parent.postMessage({action: 'mermaidClicked', success: true}, '*');
                            return;
                        }
                    }
                    window.parent.postMessage({action: 'mermaidClicked', success: false}, '*');
                }
            `;

            try {
                iframe.contentWindow.postMessage({
                    action: 'injectScript',
                    script: clickScript
                }, '*');
            } catch (error) {
                showStatus('Error injecting script: ' + error.message, 'error');
            }
        }

        function pasteMermaidCode(mermaidCode) {
            const iframe = document.getElementById('drawio-iframe');
            
            const pasteScript = `
                // Wait for Mermaid dialog to appear
                function waitForMermaidDialog() {
                    const dialogs = document.querySelectorAll('.mxWindow, [role="dialog"]');
                    for (let dialog of dialogs) {
                        const text = dialog.textContent || dialog.innerText || '';
                        if (text.toLowerCase().includes('mermaid')) {
                            // Found Mermaid dialog - look for textarea
                            const textareas = dialog.querySelectorAll('textarea');
                            for (let textarea of textareas) {
                                if (textarea.offsetHeight > 0) {
                                    // Found textarea - paste code
                                    textarea.value = \`${mermaidCode.replace(/`/g, '\\`')}\`;
                                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                                    
                                    // Look for insert/okay button
                                    const buttons = dialog.querySelectorAll('button');
                                    for (let button of buttons) {
                                        const btnText = button.textContent || button.innerText || '';
                                        if (btnText.toLowerCase().includes('insert') || 
                                            btnText.toLowerCase().includes('ok') ||
                                            btnText.toLowerCase().includes('apply')) {
                                            setTimeout(() => {
                                                button.click();
                                                window.parent.postMessage({action: 'mermaidPasted', success: true}, '*');
                                            }, 500);
                                            return;
                                        }
                                    }
                                    
                                    window.parent.postMessage({action: 'mermaidPasted', success: false, reason: 'No insert button found'}, '*');
                                    return;
                                }
                            }
                        }
                    }
                    
                    // Retry after delay
                    setTimeout(waitForMermaidDialog, 500);
                }
                
                waitForMermaidDialog();
            `;

            try {
                iframe.contentWindow.postMessage({
                    action: 'injectScript',
                    script: pasteScript
                }, '*');
            } catch (error) {
                showStatus('Error pasting Mermaid code: ' + error.message, 'error');
            }
        }

        // Global message handler for automation steps
        window.addEventListener('message', function(event) {
            if (event.data && event.data.action) {
                switch (event.data.action) {
                    case 'insertClicked':
                        if (event.data.success) {
                            showStatus('Step 2: Insert menu clicked. Looking for Mermaid option...', 'loading');
                        } else {
                            showStatus('Failed to find Insert menu. Trying alternative method...', 'error');
                            // Try alternative method
                            setTimeout(() => tryAlternativeMethod(), 1000);
                        }
                        break;
                        
                    case 'mermaidClicked':
                        if (event.data.success) {
                            showStatus('Step 3: Mermaid option clicked. Waiting for dialog...', 'loading');
                        } else {
                            showStatus('Mermaid option not found. You may need to manually select it.', 'error');
                        }
                        break;
                        
                    case 'mermaidPasted':
                        if (event.data.success) {
                            showStatus('SUCCESS: Mermaid code pasted automatically! Diagram should appear shortly.', 'success');
                        } else {
                            showStatus('Failed to paste Mermaid code: ' + (event.data.reason || 'Unknown error'), 'error');
                        }
                        break;
                }
            }
        });

        function tryAlternativeMethod() {
            const iframe = document.getElementById('drawio-iframe');
            const mermaidCode = document.getElementById('mermaid-code').value;
            
            // Alternative: Try to trigger Mermaid import directly via keyboard shortcut or other method
            const altScript = `
                // Try Ctrl+M or other shortcuts that might trigger Mermaid
                document.dispatchEvent(new KeyboardEvent('keydown', {
                    key: 'm',
                    ctrlKey: true,
                    bubbles: true
                }));
                
                // Also try to find and click any visible Mermaid-related buttons
                const allButtons = document.querySelectorAll('button, [role="button"]');
                for (let btn of allButtons) {
                    const text = btn.textContent || btn.innerText || '';
                    if (text.toLowerCase().includes('mermaid')) {
                        btn.click();
                        window.parent.postMessage({action: 'mermaidClicked', success: true}, '*');
                        return;
                    }
                }
                
                window.parent.postMessage({action: 'alternativeFailed'}, '*');
            `;

            try {
                iframe.contentWindow.postMessage({
                    action: 'injectScript',
                    script: altScript
                }, '*');
            } catch (error) {
                showStatus('Alternative method failed: ' + error.message, 'error');
            }
        }

        // Script injection handler
        window.addEventListener('message', function(event) {
            if (event.data && event.data.action === 'injectScript' && event.source === iframe.contentWindow) {
                // This is the Draw.io iframe asking us to inject a script (for cross-origin communication)
                try {
                    const script = document.createElement('script');
                    script.textContent = event.data.script;
                    iframe.contentWindow.document.head.appendChild(script);
                } catch (error) {
                    console.error('Script injection failed:', error);
                }
            }
        });

        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 5000);
            }
        }

        // Load sample on startup
        window.onload = loadSample;
    </script>
</body>
</html>
