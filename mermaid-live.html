<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Mermaid Live Editor — Single File</title>
  <style>
    :root{--bg:#0f1724;--card:#0b1220;--muted:#9aa4b2;--accent:#60a5fa;--glass: rgba(255,255,255,0.03)}
    html,body{height:100%;margin:0;font-family:Inter,ui-sans-serif,system-ui,Segoe UI,Roboto,'Helvetica Neue',Arial}
    body{background:linear-gradient(180deg,#071029 0%, #071728 100%);color:#e6eef8;display:flex;align-items:stretch}
    .app{display:grid;grid-template-columns:480px 1fr;gap:18px;padding:22px;width:100%;box-sizing:border-box}

    .panel{background:var(--card);border-radius:12px;padding:14px;box-shadow:0 6px 18px rgba(2,6,23,0.6);min-height:80vh;display:flex;flex-direction:column}
    .panel h2{margin:0 0 8px 0;font-size:18px}
    textarea#source{width:100%;height:calc(100% - 130px);resize:none;background:var(--glass);color:inherit;border:1px solid rgba(255,255,255,0.04);padding:12px;border-radius:8px;font-family:ui-monospace,Menlo,Monaco,Consolas,'Courier New',monospace;font-size:13px}
    .controls{display:flex;gap:8px;align-items:center;margin-bottom:10px}
    .btn{background:transparent;border:1px solid rgba(255,255,255,0.06);padding:8px 10px;border-radius:8px;color:var(--muted);cursor:pointer}
    .btn.primary{border-color:var(--accent);color:white}
    .small{font-size:13px;padding:6px 8px}
    .right{margin-left:auto;display:flex;gap:8px}

    .viewer{padding:18px;background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));border-radius:12px;min-height:80vh;overflow:auto}
    .viewer .toolbar{display:flex;align-items:center;gap:10px;margin-bottom:10px}
    .viewer .canvas{background:transparent;padding:12px;border-radius:8px;min-height:60vh;display:flex;align-items:flex-start;justify-content:flex-start}
    select,input[type=checkbox]{background:transparent;color:var(--muted);border:1px solid rgba(255,255,255,0.04);padding:6px;border-radius:6px}
    .muted{color:var(--muted);font-size:13px}
    .footer{font-size:13px;color:var(--muted);margin-top:12px}

    @media (max-width:980px){.app{grid-template-columns:1fr;gap:12px;padding:12px}.panel{min-height:320px}}
  </style>
</head>
<body>
  <div class="app">
    <div class="panel">
      <h2>Mermaid source</h2>
      <div class="controls">
        <button class="btn primary" id="renderBtn">Render</button>
        <button class="btn" id="clearBtn">Clear</button>
        <button class="btn" id="sampleBtn">Insert sample</button>
        <label class="muted" style="margin-left:6px">Auto</label>
        <input type="checkbox" id="autoRender" title="Automatically render as you type" checked />
        <div class="right">
          <label class="muted">Theme</label>
          <select id="themeSelect">
            <option value="default">default</option>
            <option value="dark">dark</option>
            <option value="forest">forest</option>
            <option value="neutral">neutral</option>
          </select>
        </div>
      </div>

      <textarea id="source" spellcheck="false">graph LR
  A[Start] --> B{Is it ok?}
  B -- Yes --> C[Continue]
  B -- No --> D[Stop]
</textarea>

      <div style="display:flex;gap:8px;margin-top:10px;align-items:center">
        <button class="btn small" id="downloadSvg">Download SVG</button>
        <button class="btn small" id="downloadPng">Download PNG</button>
        <div class="muted" style="margin-left:auto" id="status">Ready</div>
      </div>
      <div class="footer">Paste your Mermaid code above, select theme, then Render or enable Auto to preview live.</div>
    </div>

    <div class="viewer panel">
      <div class="toolbar">
        <h2 style="margin:0">Preview</h2>
      </div>
      <div class="canvas" id="preview"><!-- rendered diagram appears here --></div>
      <div class="muted" id="error" style="margin-top:10px;display:none"></div>
    </div>
  </div>

  <!-- Mermaid from CDN -->
  <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
  <script>
    // Initialize mermaid with minimal safe defaults
    mermaid.initialize({ startOnLoad: false, securityLevel: 'loose', theme: 'default' });

    const sourceEl = document.getElementById('source');
    const preview = document.getElementById('preview');
    const renderBtn = document.getElementById('renderBtn');
    const clearBtn = document.getElementById('clearBtn');
    const sampleBtn = document.getElementById('sampleBtn');
    const autoCheckbox = document.getElementById('autoRender');
    const status = document.getElementById('status');
    const errorBox = document.getElementById('error');
    const themeSelect = document.getElementById('themeSelect');
    const downloadSvgBtn = document.getElementById('downloadSvg');
    const downloadPngBtn = document.getElementById('downloadPng');

    let debounceTimer = null;

    function setStatus(txt, isError=false){
      status.textContent = txt;
      status.style.color = isError ? '#ff9b9b' : '';
    }

    function renderMermaid() {
      const code = sourceEl.value.trim();
      errorBox.style.display = 'none';
      preview.innerHTML = '';
      if (!code) { setStatus('Nothing to render'); return; }

      // wrap code in a div.mermaid and call mermaid.init
      const wrapper = document.createElement('div');
      wrapper.className = 'mermaid';
      wrapper.textContent = code;
      preview.appendChild(wrapper);

      try{
        // update theme
        mermaid.initialize({ theme: themeSelect.value });
        // mermaid.init will render any .mermaid nodes inside preview
        mermaid.init(undefined, preview.querySelectorAll('.mermaid'));
        setStatus('Rendered at ' + new Date().toLocaleTimeString());
      }catch(e){
        // fallback: try to parse to get a better error if supported
        errorBox.style.display = 'block';
        errorBox.textContent = 'Error rendering diagram: ' + (e && e.message ? e.message : String(e));
        setStatus('Render error', true);
      }
    }

    // Debounced live render
    sourceEl.addEventListener('input', ()=>{
      if (!autoCheckbox.checked) return;
      setStatus('Typing...');
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(()=>{ renderMermaid(); }, 500);
    });

    renderBtn.addEventListener('click', ()=>{ renderMermaid(); });
    clearBtn.addEventListener('click', ()=>{ sourceEl.value = ''; renderMermaid(); setStatus('Cleared'); });
    sampleBtn.addEventListener('click', ()=>{
      sourceEl.value = `sequenceDiagram\n    Alice->>Bob: Hello Bob, how are you?\n    Bob-->>Alice: I am good thanks!`;
      renderMermaid();
    });

    themeSelect.addEventListener('change', ()=>{ renderMermaid(); });

    // Download functions
    function download(filename, dataUrl) {
      const a = document.createElement('a');
      a.href = dataUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      a.remove();
    }

    downloadSvgBtn.addEventListener('click', ()=>{
      const svg = preview.querySelector('svg');
      if(!svg){ setStatus('No diagram to download', true); return; }
      const svgStr = svg.outerHTML;
      const blob = new Blob([svgStr], {type: 'image/svg+xml;charset=utf-8'});
      const url = URL.createObjectURL(blob);
      download('diagram.svg', url);
      setTimeout(()=>URL.revokeObjectURL(url), 2000);
    });

    downloadPngBtn.addEventListener('click', ()=>{
      const svg = preview.querySelector('svg');
      if(!svg){ setStatus('No diagram to download', true); return; }
      const svgStr = svg.outerHTML;
      const svgBlob = new Blob([svgStr], {type: 'image/svg+xml;charset=utf-8'});
      const DOMURL = self.URL || self.webkitURL || self;
      const url = DOMURL.createObjectURL(svgBlob);
      const img = new Image();
      img.onload = function(){
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        // white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0,0,canvas.width,canvas.height);
        ctx.drawImage(img,0,0);
        const png = canvas.toDataURL('image/png');
        download('diagram.png', png);
        DOMURL.revokeObjectURL(url);
      };
      img.onerror = function(e){ setStatus('Failed to convert SVG to PNG', true); DOMURL.revokeObjectURL(url); };
      img.src = url;
    });

    // initial render
    renderMermaid();
  </script>
</body>
</html>
