from flask import Flask, send_from_directory, jsonify
import base64, os

app = Flask(__name__, static_folder='.')

@app.route('/')
def serve_index():
    return send_from_directory('.', 'index.html')

@app.route('/diagram/<path:filename>')
def get_diagram(filename):
    path = os.path.join('diagrams', filename)
    if not os.path.exists(path):
        return jsonify({"error": "File not found"}), 404

    with open(path, 'rb') as f:
        data = f.read()
        b64 = base64.b64encode(data).decode('utf-8')
        return b64

if __name__ == '__main__':
    app.run(debug=True, port=8000)
    