<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Diagram Editor with Draw.io</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            display: flex;
            height: calc(100vh - 180px);
            min-height: 600px;
        }

        .editor-panel {
            width: 35%;
            background: #f8f9fa;
            padding: 25px;
            border-right: 2px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .editor-panel h2 {
            font-size: 18px;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .editor-panel h2::before {
            content: "📝";
            font-size: 20px;
        }

        #mermaidInput {
            flex: 1;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: none;
            background: white;
            transition: border-color 0.3s;
        }

        #mermaidInput:focus {
            outline: none;
            border-color: #667eea;
        }

        .button-group {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        button {
            flex: 1;
            min-width: 120px;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .render-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .render-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .export-btn {
            background: #FF9800;
            color: white;
        }

        .export-btn:hover {
            background: #F57C00;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
        }

        .clear-btn {
            background: #f44336;
            color: white;
        }

        .clear-btn:hover {
            background: #d32f2f;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
        }

        .example-btn {
            background: #4CAF50;
            color: white;
        }

        .example-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .drawio-btn {
            background: #9C27B0;
            color: white;
        }

        .drawio-btn:hover {
            background: #7B1FA2;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(156, 39, 176, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .canvas-panel {
            flex: 1;
            background: white;
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .canvas-container {
            flex: 1;
            position: relative;
            overflow: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        #diagramOutput {
            max-width: 100%;
            max-height: 100%;
            display: none;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        #drawioEditor {
            width: 100%;
            height: 100%;
            display: none;
            border: 1px solid #ddd;
            background: white;
        }

        .placeholder {
            text-align: center;
            color: #999;
            font-size: 16px;
        }

        .placeholder svg {
            width: 100px;
            height: 100px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .status {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(76, 175, 80, 0.95);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 13px;
            display: none;
            animation: fadeIn 0.3s;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status.error {
            background: rgba(244, 67, 54, 0.95);
        }

        .status.show {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -10px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }

        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 12px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            font-size: 13px;
            color: #1565C0;
            line-height: 1.6;
        }

        .error-box {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 12px 15px;
            margin-top: 15px;
            border-radius: 4px;
            font-size: 13px;
            color: #c62828;
            display: none;
        }

        .toolbar {
            display: flex;
            padding: 10px 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar button {
            min-width: auto;
            flex: none;
            padding: 8px 12px;
        }

        .view-selector {
            display: flex;
            margin-left: auto;
        }

        .view-selector button {
            border-radius: 0;
            border: 1px solid #ddd;
        }

        .view-selector button:first-child {
            border-radius: 4px 0 0 4px;
        }

        .view-selector button:last-child {
            border-radius: 0 4px 4px 0;
        }

        .view-selector button.active {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .content {
                flex-direction: column;
                height: auto;
            }

            .editor-panel {
                width: 100%;
                border-right: none;
                border-bottom: 2px solid #e0e0e0;
            }

            #mermaidInput {
                min-height: 200px;
            }

            .canvas-panel {
                min-height: 400px;
            }
        }

        /* mxGraph styles */
        .mxRubberband {
            position: absolute;
            overflow: hidden;
            border-style: solid;
            border-width: 1px;
            border-color: #0000FF;
            background: #0077FF;
        }
        
        .mxWindow {
            -webkit-box-shadow: 3px 3px 12px #C0C0C0;
            -moz-box-shadow: 3px 3px 12px #C0C0C0;
            box-shadow: 3px 3px 12px #C0C0C0;
            background: url('data:image/gif;base64,R0lGODlhGgAaAIAAAP7+/gAAACH5BAEAAAEALAAAAAAaABoAAAImjI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6zvf+DwwKh8QhAAA7');
            border: 1px solid #c3c3c3;
            position: absolute;
            overflow: hidden;
            z-index: 1;
        }
        
        .mxWindowTitle {
            background: #EDEDED;
            border-bottom: 1px solid #c3c3c3;
            padding: 2px 4px;
            font-weight: bold;
            color: #000000;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .mxWindowPane {
            padding: 8px;
            background: white;
        }
        
        .mxToolbar {
            position: absolute;
            padding: 2px;
            background: #EDEDED;
            border: 1px solid #c3c3c3;
        }
        
        .mxCellEditor {
            background: url('data:image/gif;base64,R0lGODlhMAAwAIAAAP///wAAACH5BAEAAAAALAAAAAAwADAAAAIxhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6zvf+DwwKh8QhAAA7');
            border-color: transparent;
            border-style: solid;
            border-width: 0;
            display: inline-block;
            position: absolute;
            overflow: visible;
            word-wrap: normal;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Mermaid Diagram Editor</h1>
            <p>Create beautiful diagrams with Mermaid.js - Edit with Draw.io tools</p>
        </div>
        <div class="content">
            <div class="editor-panel">
                <h2>Mermaid Code Editor</h2>
                <div class="info-box">
                    💡 <strong>How to use:</strong><br>
                    1. Type or paste Mermaid code below<br>
                    2. Click "Render Diagram" to preview<br>
                    3. Use "Open in Draw.io" to edit with full tools<br>
                    4. Export as PNG or SVG when done
                </div>
                <textarea id="mermaidInput" placeholder="graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Debug]
    D --> B
    C --> E[End]"></textarea>
                <div class="error-box" id="errorBox"></div>
                <div class="button-group">
                    <button class="render-btn" onclick="renderDiagram()">🎨 Render Diagram</button>
                    <button class="example-btn" onclick="loadExample()">📋 Load Example</button>
                    <button class="drawio-btn" onclick="openInDrawio()" id="drawioBtn" disabled>🎯 Open in Draw.io</button>
                    <button class="export-btn" onclick="exportPNG()" id="exportBtn" disabled>💾 Export PNG</button>
                    <button class="export-btn" onclick="exportSVG()" id="svgBtn" disabled>📄 Export SVG</button>
                    <button class="clear-btn" onclick="clearAll()">🗑️ Clear</button>
                </div>
            </div>
            <div class="canvas-panel">
                <div class="toolbar">
                    <div class="view-selector">
                        <button id="mermaidViewBtn" class="active" onclick="switchView('mermaid')">Mermaid View</button>
                        <button id="drawioViewBtn" onclick="switchView('drawio')">Draw.io Editor</button>
                    </div>
                </div>
                <div class="canvas-container">
                    <div class="placeholder" id="placeholder">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14 2 14 8 20 8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10 9 9 9 8 9"></polyline>
                        </svg>
                        <p>Your diagram will appear here</p>
                        <p style="font-size: 14px; margin-top: 10px;">Click "Render Diagram" to start</p>
                    </div>
                    <div id="diagramOutput"></div>
                    <div id="drawioEditor"></div>
                </div>
                <div class="status" id="status"></div>
            </div>
        </div>
    </div>

    <script>
        fetch("http://localhost:8000/diagram/test.drawio")
  .then(res => res.text())
  .then(base64data => {
    const xml = atob(base64data); // now works, since it's valid base64
    console.log("Decoded XML:", xml);
    // proceed to load xml into Draw.io or Mermaid
  })
  .catch(err => console.error("Error loading diagram:", err));

        mermaid.initialize({ 
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        const input = document.getElementById('mermaidInput');
        const output = document.getElementById('diagramOutput');
        const placeholder = document.getElementById('placeholder');
        const status = document.getElementById('status');
        const errorBox = document.getElementById('errorBox');
        const exportBtn = document.getElementById('exportBtn');
        const svgBtn = document.getElementById('svgBtn');
        const drawioBtn = document.getElementById('drawioBtn');
        const drawioEditor = document.getElementById('drawioEditor');
        const mermaidViewBtn = document.getElementById('mermaidViewBtn');
        const drawioViewBtn = document.getElementById('drawioViewBtn');

        let currentSvg = null;
        let graph = null;
        let currentView = 'mermaid';

        // Configure mxGraph to avoid file system access
        mxLoadResources = false;
        mxLoadStylesheets = false;

        async function renderDiagram() {
            const code = input.value.trim();
            
            if (!code) {
                showStatus('Please enter some Mermaid code first!', true);
                return;
            }

            try {
                // Clear previous diagram
                output.innerHTML = '';
                errorBox.style.display = 'none';
                
                // Generate unique ID
                const id = 'mermaid-' + Date.now();
                
                // Render the mermaid diagram
                const { svg } = await mermaid.render(id, code);
                
                // Display the rendered SVG
                output.innerHTML = svg;
                output.style.display = 'block';
                placeholder.style.display = 'none';
                
                // Store the SVG for export
                currentSvg = svg;
                
                // Enable export buttons
                exportBtn.disabled = false;
                svgBtn.disabled = false;
                drawioBtn.disabled = false;
                
                showStatus('Diagram rendered successfully!', false);
                
            } catch (error) {
                errorBox.textContent = '❌ Error: ' + error.message;
                errorBox.style.display = 'block';
                showStatus('Failed to render diagram. Check your Mermaid syntax.', true);
                console.error('Mermaid error:', error);
                
                // Disable export buttons
                exportBtn.disabled = true;
                svgBtn.disabled = true;
                drawioBtn.disabled = true;
            }
        }
function openInDrawio() {
    if (!currentSvg) {
        showStatus('Please render a diagram first!', true);
        return;
    }

    try {
        const svgElement = output.querySelector('svg');
        const svgData = new XMLSerializer().serializeToString(svgElement);

        // 1. Compress the SVG
        const utf8 = new TextEncoder().encode(svgData);
        const compressed = pako.deflateRaw(utf8);

        // 2. Encode to Base64
        let binary = '';
        const bytes = new Uint8Array(compressed);
        for (let i = 0; i < bytes.length; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        let base64 = btoa(binary);

        // 3. Make Base64 URL-safe (Draw.io expects this)
        base64 = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

        // 4. Build Draw.io URL
        const drawioUrl = `https://app.diagrams.net/?title=MermaidDiagram.svg#R${base64}`;

        // 5. Open it
        window.open(drawioUrl, '_blank');
        showStatus('Opening in Draw.io...', false);
    } catch (error) {
        showStatus('Error opening in Draw.io: ' + error.message, true);
        console.error('Draw.io open error:', error);
    }
}
        function initializeDrawioEditor(xmlData) {
            // Clear previous editor
            drawioEditor.innerHTML = '';
            
            // Create a container for the editor
            const editorContainer = document.createElement('div');
            editorContainer.style.width = '100%';
            editorContainer.style.height = '100%';
            editorContainer.style.background = 'white';
            drawioEditor.appendChild(editorContainer);
            
            // Check if browser is supported
            if (!mxClient.isBrowserSupported()) {
                showStatus('Browser is not supported!', true);
                return;
            }
            
            // Disable built-in context menu
            mxEvent.disableContextMenu(editorContainer);
            
            // Create graph
            graph = new mxGraph(editorContainer);
            
            // Configure graph
            graph.setConnectable(true);
            graph.setPanning(true);
            graph.setTooltips(true);
            graph.setAllowDanglingEdges(false);
            
            // Enable rubberband selection
            new mxRubberband(graph);
            
            // Configure styles
            const style = graph.getStylesheet().getDefaultVertexStyle();
            style[mxConstants.STYLE_FONTSIZE] = '12';
            style[mxConstants.STYLE_FONTCOLOR] = 'black';
            style[mxConstants.STYLE_STROKECOLOR] = 'black';
            style[mxConstants.STYLE_FILLCOLOR] = 'white';
            
            // Load the XML data
            try {
                const doc = mxUtils.parseXml(xmlData);
                const codec = new mxCodec(doc);
                codec.decode(doc.documentElement, graph.getModel());
            } catch (e) {
                console.error('Error loading XML:', e);
                showStatus('Error loading diagram in Draw.io editor', true);
            }
            
            // Center the diagram
            graph.center();
            
            // Show the editor
            drawioEditor.style.display = 'block';
        }

        function switchView(view) {
            currentView = view;
            
            if (view === 'mermaid') {
                mermaidViewBtn.classList.add('active');
                drawioViewBtn.classList.remove('active');
                output.style.display = 'block';
                drawioEditor.style.display = 'none';
            } else {
                mermaidViewBtn.classList.remove('active');
                drawioViewBtn.classList.add('active');
                output.style.display = 'none';
                drawioEditor.style.display = 'block';
            }
        }

        function exportPNG() {
            if (!currentSvg) {
                showStatus('Please render a diagram first!', true);
                return;
            }

            try {
                // Create a canvas to convert SVG to PNG
                const svgElement = output.querySelector('svg');
                const svgData = new XMLSerializer().serializeToString(svgElement);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                // Get SVG dimensions
                const svgWidth = svgElement.width.baseVal.value || 800;
                const svgHeight = svgElement.height.baseVal.value || 600;
                
                canvas.width = svgWidth;
                canvas.height = svgHeight;

                img.onload = function() {
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.drawImage(img, 0, 0);
                    
                    // Convert to PNG and download
                    canvas.toBlob(function(blob) {
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'mermaid-diagram.png';
                        a.click();
                        URL.revokeObjectURL(url);
                        showStatus('PNG exported successfully!', false);
                    });
                };

                img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
                
            } catch (error) {
                showStatus('Error exporting PNG: ' + error.message, true);
                console.error('Export error:', error);
            }
        }

        function exportSVG() {
            if (!currentSvg) {
                showStatus('Please render a diagram first!', true);
                return;
            }

            try {
                const svgElement = output.querySelector('svg');
                const svgData = new XMLSerializer().serializeToString(svgElement);
                
                // Create a blob and download
                const blob = new Blob([svgData], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'mermaid-diagram.svg';
                a.click();
                URL.revokeObjectURL(url);
                
                showStatus('SVG exported successfully!', false);
                
            } catch (error) {
                showStatus('Error exporting SVG: ' + error.message, true);
                console.error('Export error:', error);
            }
        }

        function loadExample() {
            const examples = [
                `graph TD
    A[Client Browser] -->|HTTPS Request| B[Load Balancer]
    B --> C[Web Server 1]
    B --> D[Web Server 2]
    B --> E[Web Server 3]
    C --> F[(Database Master)]
    D --> F
    E --> F
    F -.->|Replication| G[(Database Replica)]
    C -->|JSON Response| A
    D -->|JSON Response| A
    E -->|JSON Response| A`,
                `sequenceDiagram
    participant User
    participant Browser
    participant Server
    participant Database
    
    User->>Browser: Enter URL
    Browser->>Server: HTTP Request
    Server->>Database: Query Data
    Database-->>Server: Return Data
    Server-->>Browser: HTTP Response
    Browser-->>User: Display Page`,
                `classDiagram
    class Animal {
        +String name
        +int age
        +makeSound()
        +eat()
    }
    class Dog {
        +String breed
        +bark()
    }
    class Cat {
        +String color
        +meow()
    }
    Animal <|-- Dog
    Animal <|-- Cat`,
                `pie title Programming Languages Usage
    "JavaScript" : 35
    "Python" : 25
    "Java" : 20
    "C++" : 10
    "Others" : 10`
            ];
            
            const randomExample = examples[Math.floor(Math.random() * examples.length)];
            input.value = randomExample;
            input.focus();
            showStatus('Example loaded! Click "Render Diagram" to view.', false);
        }

        function clearAll() {
            input.value = '';
            output.innerHTML = '';
            output.style.display = 'none';
            drawioEditor.style.display = 'none';
            placeholder.style.display = 'block';
            errorBox.style.display = 'none';
            currentSvg = null;
            exportBtn.disabled = true;
            svgBtn.disabled = true;
            drawioBtn.disabled = true;
            mermaidViewBtn.classList.add('active');
            drawioViewBtn.classList.remove('active');
            currentView = 'mermaid';
            input.focus();
        }

        function showStatus(message, isError = false) {
            status.textContent = message;
            status.className = 'status show' + (isError ? ' error' : '');
            
            setTimeout(() => {
                status.classList.remove('show');
            }, 3000);
        }

        // Auto-focus on input when page loads
        window.addEventListener('load', () => {
            input.focus();
        });

        // Optional: Render on Ctrl+Enter
        input.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                renderDiagram();
            }
        });
    </script>
</body>
</html>