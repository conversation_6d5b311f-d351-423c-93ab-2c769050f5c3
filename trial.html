<!DOCTYPE html>
<html>
<head>
    <title>Your Diagram App</title>
</head>
<body>
    <!-- Your app interface -->
    <div id="app">
        <textarea id="user-prompt">Create a flowchart for user login</textarea>
        <button onclick="generateDiagram()">Generate Diagram</button>
        
        <!-- This becomes the FULL draw.io editor -->
        <iframe 
            id="drawio-editor"
            style="width: 100%; height: 600px; border: 1px solid #ddd; display: none;"
            src="https://embed.diagrams.net/?embed=1&ui=atlas&spin=1&proto=json">
        </iframe>
        
        <div id="editor-controls" style="display: none;">
            <button onclick="saveEdits()">Save Changes</button>
            <button onclick="closeEditor()">Close Editor</button>
        </div>
    </div>

    <script>
        class DiagramApp {
            constructor() {
                this.currentMermaidCode = '';
                this.drawIoIframe = document.getElementById('drawio-editor');
            }
            
            async generateDiagram() {
                const prompt = document.getElementById('user-prompt').value;
                
                // 1. Call your LLM to generate Mermaid code
                this.currentMermaidCode = await yourLLMService.generateMermaid(prompt);
                
                // 2. Show the diagram in draw.io editor
                this.openDrawIOEditor(this.currentMermaidCode);
            }
            
            async openDrawIOEditor(mermaidCode) {
                // 3. Convert Mermaid to draw.io XML
                const drawIoXml = await this.convertToDrawIO(mermaidCode);
                
                // 4. Load into draw.io editor
                this.loadDiagramToDrawIO(drawIoXml);
                
                // 5. Show the editor to user
                document.getElementById('drawio-editor').style.display = 'block';
                document.getElementById('editor-controls').style.display = 'block';
            }
            
            async saveEdits() {
                // 6. Get modified diagram from draw.io
                const modifiedXml = await this.getDiagramFromDrawIO();
                
                // 7. Convert back to Mermaid
                const modifiedMermaid = await this.convertToMermaid(modifiedXml);
                this.currentMermaidCode = modifiedMermaid;
                
                alert('Diagram saved!');
            }
            
            // Conversion functions (you'll need to implement these)
            async convertToDrawIO(mermaidCode) {
                // Convert Mermaid syntax to draw.io XML format
                // This is the main technical work
            }
            
            async convertToMermaid(drawIoXml) {
                // Convert draw.io XML back to Mermaid
            }
            
            loadDiagramToDrawIO(xml) {
                // Send diagram to draw.io iframe
                const message = { action: 'load', xml: xml };
                this.drawIoIframe.contentWindow.postMessage(JSON.stringify(message), '*');
            }
            
            getDiagramFromDrawIO() {
                // Request diagram from draw.io iframe
                return new Promise((resolve) => {
                    const message = { action: 'export', format: 'xml' };
                    this.drawIoIframe.contentWindow.postMessage(JSON.stringify(message), '*');
                    
                    window.addEventListener('message', (event) => {
                        if (event.data && event.data.xml) {
                            resolve(event.data.xml);
                        }
                    });
                });
            }
        }
        
        const app = new DiagramApp();
        
        function generateDiagram() { app.generateDiagram(); }
        function saveEdits() { app.saveEdits(); }
        function closeEditor() { 
            document.getElementById('drawio-editor').style.display = 'none';
            document.getElementById('editor-controls').style.display = 'none';
        }
    </script>
</body>
</html>